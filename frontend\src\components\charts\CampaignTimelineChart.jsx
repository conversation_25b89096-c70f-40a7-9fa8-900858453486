import React, { useEffect, useRef, useState } from 'react';
import { format, parseISO, differenceInDays } from 'date-fns';

const CampaignTimelineChart = ({ campaigns }) => {
  const svgRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [animationProgress, setAnimationProgress] = useState(0);

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (svgRef.current) {
        const rect = svgRef.current.getBoundingClientRect();
        setDimensions({ width: rect.width, height: 300 });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Simple, smooth animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationProgress(1);
    }, 200);

    return () => clearTimeout(timer);
  }, [campaigns]);

  if (!campaigns || campaigns.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-warmGray-500">
        <div className="text-center">
          <div className="text-4xl mb-2">📊</div>
          <p>No campaign data available</p>
        </div>
      </div>
    );
  }

  // Prepare timeline data
  const timelineData = campaigns
    .map(campaign => ({
      ...campaign,
      startDate: parseISO(campaign.start_date),
      endDate: parseISO(campaign.end_date),
      duration: differenceInDays(parseISO(campaign.end_date), parseISO(campaign.start_date))
    }))
    .sort((a, b) => a.startDate - b.startDate);

  if (timelineData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-warmGray-500">
        <div className="text-center">
          <div className="text-4xl mb-2">📅</div>
          <p>No timeline data available</p>
        </div>
      </div>
    );
  }

  // Calculate timeline bounds
  const minDate = timelineData[0].startDate;
  const maxDate = timelineData[timelineData.length - 1].endDate;
  const totalDays = differenceInDays(maxDate, minDate);

  // Chart dimensions with better spacing
  const margin = { top: 50, right: 60, bottom: 80, left: 140 };
  const chartWidth = dimensions.width - margin.left - margin.right;
  const chartHeight = dimensions.height - margin.top - margin.bottom;
  const barHeight = Math.max(24, Math.min(36, chartHeight / timelineData.length - 15));

  // Scale functions
  const xScale = (date) => {
    const daysDiff = differenceInDays(date, minDate);
    return (daysDiff / totalDays) * chartWidth;
  };

  const yScale = (index) => index * (barHeight + 20) + barHeight / 2;

  return (
    <div className="w-full">
      <svg
        ref={svgRef}
        width="100%"
        height={dimensions.height}
        className="overflow-visible"
      >
        {/* Enhanced gradient definitions */}
        <defs>
          <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#E8C4A0" stopOpacity="0.9" />
            <stop offset="50%" stopColor="#DDB892" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#D4A574" stopOpacity="0.9" />
          </linearGradient>
          <filter id="softShadow">
            <feDropShadow dx="0" dy="2" stdDeviation="3" floodColor="#000" floodOpacity="0.1"/>
          </filter>
        </defs>

        {/* Chart area */}
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {/* Clean background with subtle grid */}
          <rect
            width={chartWidth}
            height={chartHeight}
            fill="#fafaf9"
            opacity="0.3"
            rx="8"
          />

          {/* Subtle horizontal grid lines */}
          {timelineData.map((_, index) => (
            <line
              key={`grid-${index}`}
              x1="0"
              y1={yScale(index)}
              x2={chartWidth}
              y2={yScale(index)}
              stroke="#e7e5e4"
              strokeWidth="0.5"
              opacity="0.3"
            />
          ))}

          {/* Timeline bars */}
          {timelineData.map((campaign, index) => {
            const x = xScale(campaign.startDate);
            const width = xScale(campaign.endDate) - x;
            const y = yScale(index) - barHeight / 2;
            const progress = animationProgress;

            return (
              <g key={campaign.id}>
                {/* Enhanced campaign bar with shadow */}
                <rect
                  x={x}
                  y={y}
                  width={width * progress}
                  height={barHeight}
                  fill="url(#timelineGradient)"
                  rx="8"
                  filter="url(#softShadow)"
                  className="transition-all duration-500 ease-out"
                />

                {/* Campaign title - better positioned and styled */}
                <text
                  x={-15}
                  y={yScale(index) + 5}
                  textAnchor="end"
                  className="text-sm font-semibold fill-warmGray-800"
                  style={{ opacity: progress }}
                >
                  {campaign.title.length > 20
                    ? `${campaign.title.substring(0, 20)}...`
                    : campaign.title
                  }
                </text>

                {/* Separated data labels with better spacing */}
                <g style={{ opacity: progress * 0.8 }}>
                  <text
                    x={x + width / 2}
                    y={y + barHeight + 18}
                    textAnchor="middle"
                    className="text-xs font-medium fill-warmGray-600"
                  >
                    {campaign.duration} days
                  </text>
                  <text
                    x={x + width / 2}
                    y={y + barHeight + 32}
                    textAnchor="middle"
                    className="text-xs fill-warmGray-500"
                  >
                    {campaign.employees_count || 0} participants
                  </text>
                </g>

                {/* Enhanced start/end markers */}
                <circle
                  cx={x}
                  cy={yScale(index)}
                  r="4"
                  fill="#E8C4A0"
                  stroke="#fff"
                  strokeWidth="2"
                  className="transition-all duration-300 ease-out"
                  style={{ opacity: progress }}
                />
                <circle
                  cx={x + width}
                  cy={yScale(index)}
                  r="4"
                  fill="#D4A574"
                  stroke="#fff"
                  strokeWidth="2"
                  className="transition-all duration-300 ease-out"
                  style={{ opacity: progress }}
                />
              </g>
            );
          })}

          {/* Simple X-axis */}
          <line
            x1="0"
            y1={chartHeight}
            x2={chartWidth}
            y2={chartHeight}
            stroke="#e5e7eb"
            strokeWidth="1"
          />

          {/* Simplified X-axis labels */}
          {[0, 0.5, 1].map((ratio, index) => {
            const x = chartWidth * ratio;
            const date = new Date(minDate.getTime() + totalDays * ratio * 24 * 60 * 60 * 1000);

            return (
              <text
                key={index}
                x={x}
                y={chartHeight + 18}
                textAnchor="middle"
                className="text-xs fill-warmGray-500"
              >
                {format(date, 'MMM yyyy')}
              </text>
            );
          })}
        </g>

        {/* Enhanced Legend with better spacing */}
        <g transform={`translate(${margin.left}, 20)`}>
          <circle cx="0" cy="0" r="4" fill="#E8C4A0" stroke="#fff" strokeWidth="2" />
          <text x="15" y="5" className="text-sm font-medium fill-warmGray-700">Campaign Start</text>

          <circle cx="120" cy="0" r="4" fill="#D4A574" stroke="#fff" strokeWidth="2" />
          <text x="135" y="5" className="text-sm font-medium fill-warmGray-700">Campaign End</text>

          <rect x="250" y="-4" width="24" height="8" fill="url(#timelineGradient)" rx="4" />
          <text x="285" y="5" className="text-sm font-medium fill-warmGray-700">Duration</text>
        </g>
      </svg>

      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div className="bg-warmGray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-warmGray-800">
            {timelineData.length}
          </div>
          <div className="text-xs text-warmGray-600">Total Campaigns</div>
        </div>
        <div className="bg-warmGray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-warmGray-800">
            {Math.round(timelineData.reduce((sum, c) => sum + c.duration, 0) / timelineData.length)}
          </div>
          <div className="text-xs text-warmGray-600">Avg Duration (days)</div>
        </div>
        <div className="bg-warmGray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-warmGray-800">
            {Math.round(totalDays / 30)}
          </div>
          <div className="text-xs text-warmGray-600">Timeline Span (months)</div>
        </div>
      </div>
    </div>
  );
};

export default CampaignTimelineChart;
